const express = require('express');
const router = express.Router();
const { ensureAuthenticated, ensureAdmin, ensureProgrammer } = require('../../middleware/auth');
const codeReviewService = require('../../services/CodeReviewService');
const SecurityAnalyzer = require('../../utils/securityAnalyzer');
const { codeUploadLimiter } = require('../../middleware/rateLimiting');
const upload = require('../../middleware/upload');

const securityAnalyzer = new SecurityAnalyzer();

// Listar revisões pendentes (CORRIGIDO)
router.get('/', ensureAuthenticated, ensureAdmin, async (req, res) => {
  try {
    console.log('🔍 Carregando painel de revisões de código...');

    const page = parseInt(req.query.page) || 1;
    const limit = 20;
    const offset = (page - 1) * limit;

    // Buscar revisões com tratamento robusto de erros
    let result;
    try {
      result = await codeReviewService.getPendingReviews(limit, offset);
      console.log('✅ Dados de revisão carregados com sucesso');
    } catch (serviceError) {
      console.error('❌ Erro no serviço de code review:', serviceError);

      // Fallback: dados vazios mas estruturados
      result = {
        reviews: [],
        total: 0,
        pending: 0,
        needsRevision: 0,
        approved: 0,
        rejected: 0,
        checkersWithoutApproval: 0,
        checkersNeedingReview: []
      };

      req.flash('error_msg', 'Erro ao carregar algumas informações de revisão. Dados básicos exibidos.');
    }

    // Garantir que todos os campos necessários existem
    const safeResult = {
      reviews: result.reviews || [],
      total: result.total || 0,
      pending: result.pending || 0,
      needsRevision: result.needsRevision || 0,
      approved: result.approved || 0,
      rejected: result.rejected || 0,
      checkersWithoutApproval: result.checkersWithoutApproval || 0,
      checkersNeedingReview: result.checkersNeedingReview || []
    };

    console.log(`📊 Renderizando painel com ${safeResult.reviews.length} revisões`);

    res.render('admin/code-reviews/index', {
      title: 'Revisões de Código - CancroSoft TM',
      user: req.session.user,
      reviews: safeResult.reviews,
      pagination: {
        page,
        totalPages: Math.ceil(safeResult.total / limit) || 1,
        total: safeResult.total
      },
      stats: {
        pending: safeResult.pending,
        needsRevision: safeResult.needsRevision,
        approved: safeResult.approved,
        rejected: safeResult.rejected,
        checkersWithoutApproval: safeResult.checkersWithoutApproval,
        checkersNeedingReview: safeResult.checkersNeedingReview
      }
    });

  } catch (err) {
    console.error('❌ Erro crítico ao carregar painel de revisões:', err);
    req.flash('error_msg', 'Erro crítico ao carregar revisões. Tente novamente.');
    res.redirect('/admin');
  }
});

// Visualizar detalhes de uma revisão
router.get('/:id', ensureAuthenticated, ensureAdmin, async (req, res) => {
  try {
    const reviewId = req.params.id;
    const review = await codeReviewService.getReviewDetails(reviewId);

    if (!review) {
      req.flash('error_msg', 'Revisão não encontrada');
      return res.redirect('/admin/code-reviews');
    }

    res.render('admin/code-reviews/detail', {
      title: `Revisão #${review.id} - CancroSoft TM`,
      user: req.session.user,
      review
    });
  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao carregar revisão');
    res.redirect('/admin/code-reviews');
  }
});

// Aprovar revisão
router.post('/:id/approve', ensureAuthenticated, ensureAdmin, async (req, res) => {
  try {
    const reviewId = req.params.id;
    const { notes } = req.body;

    await codeReviewService.approveCodeReview(reviewId, req.session.user.id, notes);

    req.flash('success_msg', 'Código aprovado com sucesso');
    res.redirect(`/admin/code-reviews/${reviewId}`);
  } catch (err) {
    console.error(err);
    req.flash('error_msg', err.message || 'Erro ao aprovar código');
    res.redirect(`/admin/code-reviews/${req.params.id}`);
  }
});

// Rejeitar revisão
router.post('/:id/reject', ensureAuthenticated, ensureAdmin, async (req, res) => {
  try {
    const reviewId = req.params.id;
    const { reason } = req.body;

    if (!reason || reason.trim().length === 0) {
      req.flash('error_msg', 'Motivo da rejeição é obrigatório');
      return res.redirect(`/admin/code-reviews/${reviewId}`);
    }

    await codeReviewService.rejectCodeReview(reviewId, req.session.user.id, reason);

    req.flash('success_msg', 'Código rejeitado');
    res.redirect(`/admin/code-reviews/${reviewId}`);
  } catch (err) {
    console.error(err);
    req.flash('error_msg', err.message || 'Erro ao rejeitar código');
    res.redirect(`/admin/code-reviews/${req.params.id}`);
  }
});

// Solicitar revisão
router.post('/:id/request-revision', ensureAuthenticated, ensureAdmin, async (req, res) => {
  try {
    const reviewId = req.params.id;
    const { notes } = req.body;

    if (!notes || notes.trim().length === 0) {
      req.flash('error_msg', 'Notas de revisão são obrigatórias');
      return res.redirect(`/admin/code-reviews/${reviewId}`);
    }

    await codeReviewService.requestRevision(reviewId, req.session.user.id, notes);

    req.flash('success_msg', 'Revisão solicitada');
    res.redirect(`/admin/code-reviews/${reviewId}`);
  } catch (err) {
    console.error(err);
    req.flash('error_msg', err.message || 'Erro ao solicitar revisão');
    res.redirect(`/admin/code-reviews/${req.params.id}`);
  }
});

// Página para submeter código (para programadores)
router.get('/submit/new', ensureAuthenticated, ensureProgrammer, (req, res) => {
  res.render('admin/code-reviews/submit', {
    title: 'Submeter Código - CancroSoft TM',
    user: req.session.user
  });
});

// Criar revisões faltantes para checkers sem aprovação
router.post('/create-missing', ensureAuthenticated, ensureAdmin, async (req, res) => {
  try {
    const result = await codeReviewService.createMissingCodeReviews();

    res.json({
      success: true,
      message: `${result.processedCheckers} checkers processados`,
      processedCheckers: result.processedCheckers,
      results: result.results
    });

  } catch (err) {
    console.error('Erro ao criar revisões faltantes:', err);
    res.status(500).json({
      success: false,
      message: err.message || 'Erro interno do servidor'
    });
  }
});

// Submeter código para revisão
router.post('/submit', ensureAuthenticated, ensureProgrammer, codeUploadLimiter, upload.single('codeFile'), async (req, res) => {
  try {
    const { code, codeType, checkerId } = req.body;
    let codeContent = code;
    let originalFilename = null;

    // Se foi enviado um arquivo, usar o conteúdo do arquivo
    if (req.file) {
      const fs = require('fs');
      codeContent = fs.readFileSync(req.file.path, 'utf8');
      originalFilename = req.file.originalname;

      // Remover arquivo temporário
      fs.unlinkSync(req.file.path);
    }

    if (!codeContent || codeContent.trim().length === 0) {
      req.flash('error_msg', 'Código é obrigatório');
      return res.redirect('/admin/code-reviews/submit/new');
    }

    const result = await codeReviewService.submitCodeForReview({
      code: codeContent,
      codeType: codeType || 'custom_code',
      submittedBy: req.session.user.id,
      checkerId: checkerId || null,
      originalFilename
    });

    if (result.success) {
      req.flash('success_msg', result.message);
      res.redirect(`/admin/code-reviews/${result.reviewId}`);
    } else {
      req.flash('error_msg', 'Erro ao submeter código');
      res.redirect('/admin/code-reviews/submit/new');
    }

  } catch (err) {
    console.error(err);
    req.flash('error_msg', err.message || 'Erro ao submeter código');
    res.redirect('/admin/code-reviews/submit/new');
  }
});

// API para análise de código em tempo real
router.post('/analyze', ensureAuthenticated, ensureProgrammer, async (req, res) => {
  try {
    const { code } = req.body;

    if (!code) {
      return res.status(400).json({
        error: 'Código é obrigatório'
      });
    }

    const analysis = securityAnalyzer.analyzeCode(code);

    res.json({
      success: true,
      analysis
    });

  } catch (err) {
    console.error(err);
    res.status(500).json({
      error: 'Erro ao analisar código',
      message: err.message
    });
  }
});

// Estatísticas de revisões
router.get('/stats/overview', ensureAuthenticated, ensureAdmin, async (req, res) => {
  try {
    const stats = await codeReviewService.getReviewStats();

    res.json({
      success: true,
      stats
    });

  } catch (err) {
    console.error(err);
    res.status(500).json({
      error: 'Erro ao obter estatísticas',
      message: err.message
    });
  }
});

// Reprocessar análise de segurança
router.post('/:id/reanalyze', ensureAuthenticated, ensureAdmin, async (req, res) => {
  try {
    const reviewId = req.params.id;
    const review = await codeReviewService.getReviewDetails(reviewId);

    if (!review) {
      return res.status(404).json({
        error: 'Revisão não encontrada'
      });
    }

    // Reanalizar código
    const newAnalysis = securityAnalyzer.analyzeCode(review.code_content);

    // Atualizar revisão com nova análise
    await review.update({
      security_score: newAnalysis.score,
      security_issues: newAnalysis.issues,
      execution_sandbox: securityAnalyzer.determineSandboxLevel(newAnalysis)
    });

    res.json({
      success: true,
      message: 'Análise reprocessada',
      analysis: newAnalysis
    });

  } catch (err) {
    console.error(err);
    res.status(500).json({
      error: 'Erro ao reprocessar análise',
      message: err.message
    });
  }
});

// Exportar código aprovado
router.get('/:id/export', ensureAuthenticated, ensureAdmin, async (req, res) => {
  try {
    const reviewId = req.params.id;
    const review = await codeReviewService.getReviewDetails(reviewId);

    if (!review) {
      req.flash('error_msg', 'Revisão não encontrada');
      return res.redirect('/admin/code-reviews');
    }

    if (review.status !== 'approved') {
      req.flash('error_msg', 'Apenas códigos aprovados podem ser exportados');
      return res.redirect(`/admin/code-reviews/${reviewId}`);
    }

    const filename = review.original_filename || `checker_${review.id}.js`;

    res.setHeader('Content-Type', 'application/javascript');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.send(review.code_content);

  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao exportar código');
    res.redirect(`/admin/code-reviews/${req.params.id}`);
  }
});

// Histórico de revisões de um usuário
router.get('/user/:userId/history', ensureAuthenticated, ensureAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const page = parseInt(req.query.page) || 1;
    const limit = 20;

    const { CodeReview, User } = require('../../models');

    const { count, rows } = await CodeReview.findAndCountAll({
      where: { submitted_by: userId },
      include: [
        { model: User, as: 'reviewer', attributes: ['id', 'usuario'], required: false }
      ],
      order: [['submitted_at', 'DESC']],
      limit,
      offset: (page - 1) * limit
    });

    res.render('admin/code-reviews/user-history', {
      title: 'Histórico de Revisões - CancroSoft TM',
      user: req.session.user,
      reviews: rows,
      userId,
      pagination: {
        page,
        totalPages: Math.ceil(count / limit),
        total: count
      }
    });

  } catch (err) {
    console.error(err);
    req.flash('error_msg', 'Erro ao carregar histórico');
    res.redirect('/admin/code-reviews');
  }
});

module.exports = router;
