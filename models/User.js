const { DataTypes } = require('sequelize');
const bcrypt = require('bcryptjs');
const db = require('../config/database');

const User = db.define('usuario', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  usuario: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: true
  },
  senha: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  email: {
    type: DataTypes.STRING(255),
    allowNull: false,
    unique: true,
    validate: {
      isEmail: true
    }
  },
  saldo: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0.00
  },
  rank: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  role: {
    type: DataTypes.ENUM('user', 'programmer', 'admin'),
    allowNull: false,
    defaultValue: 'user'
  },
  criador: {
    type: DataTypes.STRING(255),
    allowNull: true,
    defaultValue: null
  },
  referral_code: {
    type: DataTypes.STRING(50),
    allowNull: true,
    unique: true
  },
  referred_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'usuarios',
      key: 'id'
    }
  },
  lives: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  status: {
    type: DataTypes.ENUM('active', 'suspended', 'banned'),
    defaultValue: 'active'
  },
  lastLogin: {
    type: DataTypes.DATE,
    allowNull: true
  },
  resetPasswordToken: {
    type: DataTypes.STRING,
    allowNull: true
  },
  resetPasswordExpires: {
    type: DataTypes.DATE,
    allowNull: true
  }
}, {
  timestamps: false,
  tableName: 'usuarios'
});

// Hash password before saving
User.beforeCreate(async (user) => {
  // Check if the password is already hashed (MD5)
  if (user.senha.length !== 32) {
    const salt = await bcrypt.genSalt(10);
    user.senha = await bcrypt.hash(user.senha, salt);
  }
});

// Method to validate password
User.prototype.validPassword = async function(password) {
  // First try bcrypt comparison
  try {
    return await bcrypt.compare(password, this.senha);
  } catch (err) {
    // If bcrypt fails, it might be an MD5 hash from the old system
    const crypto = require('crypto');
    const md5Hash = crypto.createHash('md5').update(password).digest('hex');
    return md5Hash === this.senha;
  }
};

module.exports = User;
