<div class="row">
  <div class="col-md-12">
    <div class="card">
      <div class="card-header">
        <h4 class="card-title text-center">
          <i class="fa fa-code text-primary"></i> 
          <b>REVISÕES DE CÓDIGO</b>
        </h4>
        <div class="text-center mt-2">
          <span class="badge badge-warning">Pendentes: <%= stats.pending %></span>
          <span class="badge badge-info">Necessitam Revisão: <%= stats.needsRevision %></span>
          <% if (stats.approved) { %>
            <span class="badge badge-success">Aprovados: <%= stats.approved %></span>
          <% } %>
          <% if (stats.rejected) { %>
            <span class="badge badge-danger">Rejeitados: <%= stats.rejected %></span>
          <% } %>
          <% if (stats.checkersWithoutApproval > 0) { %>
            <span class="badge badge-dark">Checkers sem Aprovação: <%= stats.checkersWithoutApproval %></span>
          <% } %>
        </div>

        <% if (stats.checkersWithoutApproval > 0) { %>
          <div class="alert alert-warning mt-3">
            <h6><i class="fa fa-exclamation-triangle"></i> Atenção!</h6>
            <p class="mb-2">Existem <strong><%= stats.checkersWithoutApproval %></strong> checkers com código não aprovado que não podem ser executados.</p>
            <button type="button" class="btn btn-sm btn-warning" onclick="createMissingReviews()">
              <i class="fa fa-plus"></i> Criar Revisões Faltantes
            </button>
          </div>
        <% } %>
      </div>
      <div class="card-body">
        <!-- Filtros -->
        <div class="row mb-3">
          <div class="col-md-4">
            <select class="form-control" id="statusFilter">
              <option value="">Todos os Status</option>
              <option value="pending">Pendente</option>
              <option value="needs_revision">Necessita Revisão</option>
              <option value="approved">Aprovado</option>
              <option value="rejected">Rejeitado</option>
            </select>
          </div>
          <div class="col-md-4">
            <input type="text" class="form-control" id="searchInput" placeholder="Buscar por usuário ou código...">
          </div>
          <div class="col-md-4">
            <button class="btn btn-primary" onclick="refreshTable()">
              <i class="fa fa-refresh"></i> Atualizar
            </button>
          </div>
        </div>

        <!-- Tabela de Revisões -->
        <div class="table-responsive">
          <table class="table table-striped" id="reviewsTable">
            <thead>
              <tr>
                <th>ID</th>
                <th>Usuário</th>
                <th>Tipo</th>
                <th>Status</th>
                <th>Score Segurança</th>
                <th>Submetido em</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody>
              <% if (reviews && reviews.length > 0) { %>
                <% reviews.forEach(review => { %>
                  <tr>
                    <td><%= review.id %></td>
                    <td>
                      <% if (review.submitter) { %>
                        <%= review.submitter.usuario %>
                        <small class="text-muted d-block"><%= review.submitter.role %></small>
                      <% } else { %>
                        N/A
                      <% } %>
                    </td>
                    <td>
                      <span class="badge badge-secondary">
                        <%= review.code_type === 'module' ? 'Módulo' : 'Código Personalizado' %>
                      </span>
                    </td>
                    <td>
                      <% 
                        let statusClass = 'secondary';
                        let statusText = review.status;
                        switch(review.status) {
                          case 'pending':
                            statusClass = 'warning';
                            statusText = 'Pendente';
                            break;
                          case 'approved':
                            statusClass = 'success';
                            statusText = 'Aprovado';
                            break;
                          case 'rejected':
                            statusClass = 'danger';
                            statusText = 'Rejeitado';
                            break;
                          case 'needs_revision':
                            statusClass = 'info';
                            statusText = 'Necessita Revisão';
                            break;
                        }
                      %>
                      <span class="badge badge-<%= statusClass %>"><%= statusText %></span>
                    </td>
                    <td>
                      <% if (review.security_score !== null) { %>
                        <% 
                          let scoreClass = 'danger';
                          if (review.security_score >= 70) scoreClass = 'success';
                          else if (review.security_score >= 50) scoreClass = 'warning';
                        %>
                        <span class="badge badge-<%= scoreClass %>"><%= review.security_score %>%</span>
                      <% } else { %>
                        <span class="text-muted">N/A</span>
                      <% } %>
                    </td>
                    <td>
                      <%= new Date(review.submitted_at).toLocaleString('pt-BR') %>
                    </td>
                    <td>
                      <div class="btn-group" role="group">
                        <a href="/admin/code-reviews/<%= review.id %>" class="btn btn-sm btn-info">
                          <i class="fa fa-eye"></i> Ver
                        </a>
                        <% if (review.status === 'pending' || review.status === 'needs_revision') { %>
                          <button class="btn btn-sm btn-success" onclick="approveReview(<%= review.id %>)">
                            <i class="fa fa-check"></i> Aprovar
                          </button>
                          <button class="btn btn-sm btn-danger" onclick="rejectReview(<%= review.id %>)">
                            <i class="fa fa-times"></i> Rejeitar
                          </button>
                        <% } %>
                      </div>
                    </td>
                  </tr>
                <% }); %>
              <% } else { %>
                <tr>
                  <td colspan="7" class="text-center">
                    <div class="empty-state py-4">
                      <i class="fa fa-code fa-3x text-muted mb-3"></i>
                      <h5 class="text-muted">Nenhuma revisão encontrada</h5>
                      <p class="text-muted">Não há revisões de código pendentes no momento.</p>
                    </div>
                  </td>
                </tr>
              <% } %>
            </tbody>
          </table>
        </div>

        <!-- Paginação -->
        <% if (pagination && pagination.totalPages > 1) { %>
          <nav aria-label="Paginação">
            <ul class="pagination justify-content-center">
              <% if (pagination.page > 1) { %>
                <li class="page-item">
                  <a class="page-link" href="?page=<%= pagination.page - 1 %>">Anterior</a>
                </li>
              <% } %>
              
              <% for (let i = 1; i <= pagination.totalPages; i++) { %>
                <li class="page-item <%= pagination.page === i ? 'active' : '' %>">
                  <a class="page-link" href="?page=<%= i %>"><%= i %></a>
                </li>
              <% } %>
              
              <% if (pagination.page < pagination.totalPages) { %>
                <li class="page-item">
                  <a class="page-link" href="?page=<%= pagination.page + 1 %>">Próximo</a>
                </li>
              <% } %>
            </ul>
          </nav>
        <% } %>
      </div>
    </div>
  </div>
</div>

<!-- Modal para Aprovação -->
<div class="modal fade" id="approveModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Aprovar Revisão</h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form id="approveForm">
          <div class="form-group">
            <label for="approveNotes">Notas da Aprovação (opcional)</label>
            <textarea class="form-control" id="approveNotes" rows="3" placeholder="Comentários sobre a aprovação..."></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
        <button type="button" class="btn btn-success" onclick="confirmApprove()">
          <i class="fa fa-check"></i> Aprovar
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Modal para Rejeição -->
<div class="modal fade" id="rejectModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Rejeitar Revisão</h5>
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form id="rejectForm">
          <div class="form-group">
            <label for="rejectNotes">Motivo da Rejeição *</label>
            <textarea class="form-control" id="rejectNotes" rows="3" placeholder="Explique o motivo da rejeição..." required></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
        <button type="button" class="btn btn-danger" onclick="confirmReject()">
          <i class="fa fa-times"></i> Rejeitar
        </button>
      </div>
    </div>
  </div>
</div>

<script>
let currentReviewId = null;

// Filtros
document.getElementById('statusFilter').addEventListener('change', function() {
  filterTable();
});

document.getElementById('searchInput').addEventListener('keyup', function() {
  filterTable();
});

function filterTable() {
  const statusFilter = document.getElementById('statusFilter').value.toLowerCase();
  const searchFilter = document.getElementById('searchInput').value.toLowerCase();
  const table = document.getElementById('reviewsTable');
  const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');

  for (let i = 0; i < rows.length; i++) {
    const row = rows[i];
    const cells = row.getElementsByTagName('td');
    
    if (cells.length === 1) continue; // Skip empty state row
    
    const status = cells[3].textContent.toLowerCase();
    const user = cells[1].textContent.toLowerCase();
    const type = cells[2].textContent.toLowerCase();
    
    const statusMatch = !statusFilter || status.includes(statusFilter);
    const searchMatch = !searchFilter || user.includes(searchFilter) || type.includes(searchFilter);
    
    row.style.display = statusMatch && searchMatch ? '' : 'none';
  }
}

function refreshTable() {
  location.reload();
}

function approveReview(reviewId) {
  currentReviewId = reviewId;
  $('#approveModal').modal('show');
}

function createMissingReviews() {
  if (!confirm('Isso criará revisões de código para todos os checkers sem aprovação. Continuar?')) {
    return;
  }

  const btn = event.target;
  const originalText = btn.innerHTML;
  btn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Criando...';
  btn.disabled = true;

  fetch('/admin/code-reviews/create-missing', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      alert(`Sucesso! ${data.processedCheckers} checkers processados.`);
      location.reload();
    } else {
      alert('Erro: ' + (data.message || 'Erro desconhecido'));
    }
  })
  .catch(error => {
    console.error('Erro:', error);
    alert('Erro ao criar revisões: ' + error.message);
  })
  .finally(() => {
    btn.innerHTML = originalText;
    btn.disabled = false;
  });
}

function rejectReview(reviewId) {
  currentReviewId = reviewId;
  $('#rejectModal').modal('show');
}

async function confirmApprove() {
  if (!currentReviewId) return;
  
  const notes = document.getElementById('approveNotes').value;
  
  try {
    const response = await fetch(`/admin/code-reviews/${currentReviewId}/approve`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ notes })
    });
    
    const result = await response.json();
    
    if (result.success) {
      $('#approveModal').modal('hide');
      showAlert('Revisão aprovada com sucesso!', 'success');
      setTimeout(() => location.reload(), 1500);
    } else {
      showAlert(result.message || 'Erro ao aprovar revisão', 'danger');
    }
  } catch (err) {
    console.error('Erro ao aprovar:', err);
    showAlert('Erro ao aprovar revisão', 'danger');
  }
}

async function confirmReject() {
  if (!currentReviewId) return;
  
  const notes = document.getElementById('rejectNotes').value.trim();
  
  if (!notes) {
    showAlert('Por favor, informe o motivo da rejeição', 'warning');
    return;
  }
  
  try {
    const response = await fetch(`/admin/code-reviews/${currentReviewId}/reject`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ notes })
    });
    
    const result = await response.json();
    
    if (result.success) {
      $('#rejectModal').modal('hide');
      showAlert('Revisão rejeitada com sucesso!', 'success');
      setTimeout(() => location.reload(), 1500);
    } else {
      showAlert(result.message || 'Erro ao rejeitar revisão', 'danger');
    }
  } catch (err) {
    console.error('Erro ao rejeitar:', err);
    showAlert('Erro ao rejeitar revisão', 'danger');
  }
}

function showAlert(message, type) {
  const alertDiv = document.createElement('div');
  alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
  alertDiv.innerHTML = `
    ${message}
    <button type="button" class="close" data-dismiss="alert">
      <span>&times;</span>
    </button>
  `;
  
  document.querySelector('.card-body').insertBefore(alertDiv, document.querySelector('.row'));
  
  setTimeout(() => {
    alertDiv.remove();
  }, 5000);
}
</script>

<style>
.empty-state {
  padding: 2rem;
}

.badge {
  font-size: 0.75em;
}

.btn-group .btn {
  margin-right: 2px;
}

.table td {
  vertical-align: middle;
}

.modal-content {
  border-radius: 10px;
}

.card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}
</style>
